import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('../views/layout/Layout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('../views/dashboard/Dashboard.vue'),
        meta: { title: '仪表板' }
      },
      {
        path: 'organizations',
        name: 'Organizations',
        component: () => import('../views/organization/OrganizationList.vue'),
        meta: { title: '组织机构管理' }
      },
      {
        path: 'organizations/create',
        name: 'OrganizationCreate',
        component: () => import('../views/organization/OrganizationForm.vue'),
        meta: { title: '创建组织机构' }
      },
      {
        path: 'organizations/:id/edit',
        name: 'OrganizationEdit',
        component: () => import('../views/organization/OrganizationForm.vue'),
        meta: { title: '编辑组织机构' }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('../views/user/UserList.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'users/create',
        name: 'UserCreate',
        component: () => import('../views/user/UserForm.vue'),
        meta: { title: '创建用户' }
      },
      {
        path: 'users/:id/edit',
        name: 'UserEdit',
        component: () => import('../views/user/UserForm.vue'),
        meta: { title: '编辑用户' }
      },
      {
        path: 'roles',
        name: 'Roles',
        component: () => import('../views/role/RoleList.vue'),
        meta: { title: '角色管理' }
      },
      {
        path: 'roles/create',
        name: 'RoleCreate',
        component: () => import('../views/role/RoleForm.vue'),
        meta: { title: '创建角色' }
      },
      {
        path: 'roles/:id/edit',
        name: 'RoleEdit',
        component: () => import('../views/role/RoleForm.vue'),
        meta: { title: '编辑角色' }
      },
      {
        path: 'permissions',
        name: 'PermissionVisualization',
        component: () => import('../views/permission/PermissionVisualization.vue'),
        meta: { title: '权限可视化管理' }
      },
      {
        path: 'permission-test',
        name: 'PermissionTest',
        component: () => import('../views/permission/PermissionTest.vue'),
        meta: { title: '权限功能测试' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router 